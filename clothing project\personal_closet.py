"""
Personal Closet Management System
Manages user's actual clothing items with photos, details, and outfit generation
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from clothing_generator import (
    ClothingItem, OutfitGenerator, Outfit, Occasion, Weather, Style, ColorHarmony
)
from database import ClothingDatabase


@dataclass
class PersonalClothingItem(ClothingItem):
    """Extended clothing item with personal details"""
    id: str
    brand: str = ""
    size: str = ""
    material: str = ""
    purchase_date: str = ""
    cost: float = 0.0
    photo_filename: Optional[str] = None
    condition: str = "good"  # excellent, good, fair, poor
    last_worn: Optional[str] = None
    wear_count: int = 0
    notes: str = ""
    date_added: str = ""
    is_favorite: bool = False
    exclusive_occasions: List[Occasion] = None  # If set, item ONLY for these occasions
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.date_added:
            self.date_added = datetime.now().isoformat()
        if self.exclusive_occasions is None:
            self.exclusive_occasions = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        # Convert enums to strings
        data['style'] = [s.value for s in self.style]
        data['occasions'] = [o.value for o in self.occasions]
        data['weather'] = [w.value for w in self.weather]
        data['exclusive_occasions'] = [o.value for o in self.exclusive_occasions]
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PersonalClothingItem':
        """Create from dictionary"""
        # Convert string enums back to enum objects
        data['style'] = [Style(s) for s in data.get('style', [])]
        data['occasions'] = [Occasion(o) for o in data.get('occasions', [])]
        data['weather'] = [Weather(w) for w in data.get('weather', [])]
        data['exclusive_occasions'] = [Occasion(o) for o in data.get('exclusive_occasions', [])]
        return cls(**data)
    
    def update_wear_info(self):
        """Update wear information when item is worn"""
        self.wear_count += 1
        self.last_worn = datetime.now().isoformat()


@dataclass
class PersonalOutfit:
    """Personal outfit with history and preferences"""
    id: str
    items: List[PersonalClothingItem]
    occasion: Occasion
    weather: Weather
    date_created: str
    date_worn: Optional[str] = None
    rating: Optional[int] = None  # 1-5 stars
    notes: str = ""
    is_favorite: bool = False
    style_score: float = 0.0
    color_harmony: float = 0.0
    occasion_fit: float = 0.0
    overall_score: float = 0.0
    description: str = ""
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.date_created:
            self.date_created = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['items'] = [item.to_dict() for item in self.items]
        data['occasion'] = self.occasion.value
        data['weather'] = self.weather.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PersonalOutfit':
        """Create from dictionary"""
        data['items'] = [PersonalClothingItem.from_dict(item) for item in data.get('items', [])]
        data['occasion'] = Occasion(data.get('occasion', 'casual'))
        data['weather'] = Weather(data.get('weather', 'mild'))
        return cls(**data)


class PersonalCloset:
    """Personal closet management system"""

    def __init__(self, data_file: str = "data/closet.db"):
        self.data_file = data_file
        self.db = ClothingDatabase(data_file)
        self.items: Dict[str, PersonalClothingItem] = {}
        self.outfit_history: Dict[str, PersonalOutfit] = {}
        self.generator = OutfitGenerator()
        self.load_data()
    
    def load_data(self):
        """Load closet data from database"""
        try:
            # Load clothing items from database
            items = self.db.get_all_items()
            self.items = {item.id: item for item in items}

            # Load outfit history from database
            outfits = self.db.get_all_outfits()
            self.outfit_history = {outfit.id: outfit for outfit in outfits}

        except Exception as e:
            print(f"Error loading closet data: {e}")
    
    def save_data(self):
        """Save closet data to database"""
        try:
            # Save all items to database
            for item in self.items.values():
                self.db.save_item(item)

            # Save all outfits to database
            for outfit in self.outfit_history.values():
                self.db.save_outfit(outfit)

        except Exception as e:
            print(f"Error saving closet data: {e}")
    
    def add_item(self, item: PersonalClothingItem):
        """Add clothing item to closet"""
        self.items[item.id] = item
        self.db.save_item(item)
    
    def remove_item(self, item_id: str):
        """Remove clothing item from closet"""
        if item_id in self.items:
            del self.items[item_id]
            self.db.delete_item(item_id)
    
    def update_item(self, item: PersonalClothingItem):
        """Update clothing item"""
        self.items[item.id] = item
        self.db.save_item(item)
    
    def get_item_by_id(self, item_id: str) -> Optional[PersonalClothingItem]:
        """Get item by ID"""
        return self.items.get(item_id)
    
    def get_all_items(self) -> List[PersonalClothingItem]:
        """Get all clothing items"""
        return list(self.items.values())
    
    def get_items_by_category(self, category: str) -> List[PersonalClothingItem]:
        """Get items by category"""
        if category == 'all':
            return self.get_all_items()
        return [item for item in self.items.values() if item.category == category]
    
    def get_categories(self) -> List[str]:
        """Get all categories in closet"""
        categories = set(item.category for item in self.items.values())
        return sorted(list(categories))
    
    def get_items_by_occasion(self, occasion: Occasion) -> List[PersonalClothingItem]:
        """Get items suitable for occasion"""
        return [item for item in self.items.values() if occasion in item.occasions]
    
    def get_items_by_weather(self, weather: Weather) -> List[PersonalClothingItem]:
        """Get items suitable for weather"""
        return [item for item in self.items.values() if weather in item.weather]
    
    def get_items_by_style(self, style: Style) -> List[PersonalClothingItem]:
        """Get items matching style"""
        return [item for item in self.items.values() if style in item.style]
    
    def generate_personal_outfits(self, 
                                occasion: Occasion = Occasion.CASUAL,
                                weather: Weather = Weather.MILD,
                                preferred_style: Optional[Style] = None,
                                color_preference: Optional[str] = None,
                                num_suggestions: int = 3) -> List[PersonalOutfit]:
        """Generate outfits from personal closet"""
        
        # Filter personal items based on criteria
        suitable_items = self._filter_personal_items(occasion, weather, preferred_style)
        
        if not any(suitable_items.values()):
            return []
        
        # Generate outfit combinations
        outfit_combinations = self._generate_personal_combinations(suitable_items, color_preference)
        
        # Score and create personal outfits
        personal_outfits = []
        for combination in outfit_combinations:
            outfit = self._create_personal_outfit(combination, occasion, weather, preferred_style)
            if outfit.overall_score > 0.5:
                personal_outfits.append(outfit)
        
        # Sort by score and return top suggestions
        personal_outfits.sort(key=lambda x: x.overall_score, reverse=True)
        return personal_outfits[:num_suggestions]

    def generate_custom_outfits(self,
                              occasion: Occasion = Occasion.CASUAL,
                              weather: Weather = Weather.MILD,
                              preferred_style: Optional[Style] = None,
                              color_preference: Optional[str] = None,
                              custom_items: List[str] = None,
                              num_suggestions: int = 3) -> List[PersonalOutfit]:
        """Generate custom outfits with specific item types"""

        if not custom_items:
            return []

        # Filter personal items based on criteria
        suitable_items = self._filter_personal_items(occasion, weather, preferred_style)

        # Filter to only include requested item types
        filtered_custom_items = {}
        category_map = {
            'top': 'tops',
            'bottom': 'bottoms',
            'dress': 'dresses',
            'outerwear': 'outerwear',
            'shoes': 'shoes',
            'accessories': 'accessories'
        }

        for item_type in custom_items:
            category_key = category_map.get(item_type, item_type + 's')
            if category_key in suitable_items:
                filtered_custom_items[category_key] = suitable_items[category_key]

        if not any(filtered_custom_items.values()):
            return []

        # Generate custom outfit combinations
        outfit_combinations = self._generate_custom_combinations(filtered_custom_items, color_preference)

        # Score and create personal outfits
        personal_outfits = []
        for combination in outfit_combinations:
            outfit = self._create_personal_outfit(combination, occasion, weather, preferred_style)
            if outfit.overall_score > 0.3:  # Lower threshold for custom outfits
                personal_outfits.append(outfit)

        # Sort by score and return top suggestions
        personal_outfits.sort(key=lambda x: x.overall_score, reverse=True)
        return personal_outfits[:num_suggestions]

    def _filter_personal_items(self, occasion: Occasion, weather: Weather,
                             preferred_style: Optional[Style]) -> Dict[str, List[PersonalClothingItem]]:
        """Filter personal items based on criteria"""
        filtered = {
            'tops': [],
            'bottoms': [],
            'dresses': [],
            'outerwear': [],
            'shoes': [],
            'accessories': []
        }
        
        for item in self.items.values():
            # Skip items in poor condition
            if item.condition == 'poor':
                continue

            # Check exclusive occasions first - if set, item ONLY for those occasions
            if item.exclusive_occasions:
                if occasion not in item.exclusive_occasions:
                    continue
            else:
                # Regular occasion check if no exclusive occasions set
                if occasion not in item.occasions:
                    continue

            # Check weather compatibility
            if weather not in item.weather:
                continue

            # Check style preference if specified
            if preferred_style and preferred_style not in item.style:
                continue
            
            # Categorize items
            category_map = {
                'top': 'tops',
                'bottom': 'bottoms',
                'dress': 'dresses',
                'outerwear': 'outerwear',
                'shoes': 'shoes',
                'accessory': 'accessories'
            }
            
            category_key = category_map.get(item.category, 'accessories')
            filtered[category_key].append(item)
        
        return filtered
    
    def _generate_personal_combinations(self, filtered_items: Dict[str, List[PersonalClothingItem]], 
                                      color_preference: Optional[str]) -> List[List[PersonalClothingItem]]:
        """Generate outfit combinations from personal items"""
        combinations = []
        
        # Generate dress-based outfits
        for dress in filtered_items['dresses']:
            for shoes in filtered_items['shoes']:
                combo = [dress, shoes]
                
                # Add outerwear if available
                if filtered_items['outerwear']:
                    for outerwear in filtered_items['outerwear'][:2]:
                        combinations.append(combo + [outerwear])
                else:
                    combinations.append(combo)
        
        # Generate top + bottom combinations
        for top in filtered_items['tops']:
            for bottom in filtered_items['bottoms']:
                for shoes in filtered_items['shoes']:
                    combo = [top, bottom, shoes]
                    
                    # Add outerwear if available
                    if filtered_items['outerwear']:
                        for outerwear in filtered_items['outerwear'][:2]:
                            combinations.append(combo + [outerwear])
                    else:
                        combinations.append(combo)
        
        # Filter by color preference if specified
        if color_preference:
            combinations = [
                combo for combo in combinations 
                if any(color_preference.lower() in item.colors for item in combo)
            ]
        
        return combinations[:15]  # Limit combinations

    def _generate_custom_combinations(self, filtered_items: Dict[str, List[PersonalClothingItem]],
                                    color_preference: Optional[str]) -> List[List[PersonalClothingItem]]:
        """Generate custom outfit combinations based on selected item types"""
        combinations = []

        # Get available item types
        available_types = list(filtered_items.keys())

        # If only one type is selected, create single-item outfits
        if len(available_types) == 1:
            item_type = available_types[0]
            for item in filtered_items[item_type]:
                combinations.append([item])

        # If multiple types are selected, create combinations
        else:
            # Handle dress + other items
            if 'dresses' in available_types:
                for dress in filtered_items['dresses']:
                    combo = [dress]

                    # Add shoes if available
                    if 'shoes' in available_types and filtered_items['shoes']:
                        for shoes in filtered_items['shoes'][:3]:
                            current_combo = combo + [shoes]

                            # Add outerwear if available
                            if 'outerwear' in available_types and filtered_items['outerwear']:
                                for outerwear in filtered_items['outerwear'][:2]:
                                    combinations.append(current_combo + [outerwear])
                            else:
                                combinations.append(current_combo)
                    else:
                        combinations.append(combo)

            # Handle top + bottom combinations
            if 'tops' in available_types and 'bottoms' in available_types:
                for top in filtered_items['tops']:
                    for bottom in filtered_items['bottoms']:
                        combo = [top, bottom]

                        # Add shoes if available
                        if 'shoes' in available_types and filtered_items['shoes']:
                            for shoes in filtered_items['shoes'][:3]:
                                current_combo = combo + [shoes]

                                # Add outerwear if available
                                if 'outerwear' in available_types and filtered_items['outerwear']:
                                    for outerwear in filtered_items['outerwear'][:2]:
                                        combinations.append(current_combo + [outerwear])
                                else:
                                    combinations.append(current_combo)
                        else:
                            combinations.append(combo)

            # Handle individual item types when not combined with others
            elif 'tops' in available_types and 'bottoms' not in available_types:
                for top in filtered_items['tops']:
                    combo = [top]

                    # Add shoes if available
                    if 'shoes' in available_types and filtered_items['shoes']:
                        for shoes in filtered_items['shoes'][:3]:
                            combinations.append(combo + [shoes])
                    else:
                        combinations.append(combo)

            elif 'bottoms' in available_types and 'tops' not in available_types:
                for bottom in filtered_items['bottoms']:
                    combo = [bottom]

                    # Add shoes if available
                    if 'shoes' in available_types and filtered_items['shoes']:
                        for shoes in filtered_items['shoes'][:3]:
                            combinations.append(combo + [shoes])
                    else:
                        combinations.append(combo)

            # Handle accessories
            if 'accessories' in available_types:
                # Add accessories to existing combinations
                if combinations:
                    enhanced_combinations = []
                    for combo in combinations:
                        enhanced_combinations.append(combo)
                        # Add version with accessories
                        for accessory in filtered_items['accessories'][:2]:
                            enhanced_combinations.append(combo + [accessory])
                    combinations = enhanced_combinations
                else:
                    # Just accessories
                    for accessory in filtered_items['accessories']:
                        combinations.append([accessory])

        # Filter by color preference if specified
        if color_preference:
            combinations = [
                combo for combo in combinations
                if any(color_preference.lower() in ' '.join(item.colors).lower() for item in combo)
            ]

        return combinations[:20]  # Limit combinations

    def _create_personal_outfit(self, items: List[PersonalClothingItem],
                              occasion: Occasion, weather: Weather,
                              preferred_style: Optional[Style]) -> PersonalOutfit:
        """Create personal outfit with scoring"""
        
        # Convert to base ClothingItem for scoring
        base_items = [
            ClothingItem(
                name=item.name,
                category=item.category,
                colors=item.colors,
                style=item.style,
                occasions=item.occasions,
                weather=item.weather,
                formality=item.formality,
                versatility=item.versatility
            ) for item in items
        ]
        
        # Use existing scoring logic
        base_outfit = self.generator._score_outfit(base_items, occasion, preferred_style)
        
        # Create personal outfit
        personal_outfit = PersonalOutfit(
            id=str(uuid.uuid4()),
            items=items,
            occasion=occasion,
            weather=weather,
            date_created=datetime.now().isoformat(),
            style_score=base_outfit.style_score,
            color_harmony=base_outfit.color_harmony,
            occasion_fit=base_outfit.occasion_fit,
            overall_score=base_outfit.overall_score,
            description=base_outfit.description
        )
        
        return personal_outfit
    
    def save_outfit_to_history(self, outfit_data: Dict[str, Any]) -> str:
        """Save outfit to history"""
        outfit = PersonalOutfit.from_dict(outfit_data)
        self.outfit_history[outfit.id] = outfit
        self.db.save_outfit(outfit)
        return outfit.id

    def rate_outfit(self, outfit_id: str, rating: int, notes: str = "") -> bool:
        """Rate an outfit"""
        if outfit_id in self.outfit_history:
            outfit = self.outfit_history[outfit_id]
            outfit.rating = rating
            if notes:
                outfit.notes = notes
            self.db.save_outfit(outfit)
            return True
        return False
    
    def get_outfit_history(self, limit: Optional[int] = None) -> List[PersonalOutfit]:
        """Get outfit history"""
        outfits = sorted(self.outfit_history.values(), 
                        key=lambda x: x.date_created, reverse=True)
        return outfits[:limit] if limit else outfits
    
    def get_recent_outfits(self, limit: int = 5) -> List[PersonalOutfit]:
        """Get recent outfits"""
        return self.get_outfit_history(limit)
    
    def get_favorite_outfits(self) -> List[PersonalOutfit]:
        """Get favorite outfits"""
        return self.db.get_favorite_outfits()

    def toggle_outfit_favorite(self, outfit_id: str) -> bool:
        """Toggle outfit favorite status"""
        if outfit_id in self.outfit_history:
            outfit = self.outfit_history[outfit_id]
            outfit.is_favorite = not outfit.is_favorite
            self.db.save_outfit(outfit)
            return outfit.is_favorite
        return False

    def toggle_item_favorite(self, item_id: str) -> bool:
        """Toggle item favorite status"""
        if item_id in self.items:
            item = self.items[item_id]
            item.is_favorite = not item.is_favorite
            self.db.save_item(item)
            return item.is_favorite
        return False

    def get_favorite_items(self) -> List[PersonalClothingItem]:
        """Get favorite clothing items"""
        return self.db.get_favorite_items()
    
    def get_outfits_with_item(self, item_id: str) -> List[PersonalOutfit]:
        """Get outfits that include a specific item"""
        return [
            outfit for outfit in self.outfit_history.values()
            if any(item.id == item_id for item in outfit.items)
        ]
    
    def get_closet_stats(self) -> Dict[str, Any]:
        """Get closet statistics"""
        items = list(self.items.values())
        
        if not items:
            return {
                'total_items': 0,
                'categories': {},
                'total_value': 0,
                'most_worn': None,
                'least_worn': None,
                'recent_additions': []
            }
        
        # Category breakdown
        categories = {}
        for item in items:
            categories[item.category] = categories.get(item.category, 0) + 1
        
        # Value calculation
        total_value = sum(item.cost for item in items if item.cost > 0)
        
        # Wear statistics
        worn_items = [item for item in items if item.wear_count > 0]
        most_worn = max(worn_items, key=lambda x: x.wear_count) if worn_items else None
        least_worn = min(items, key=lambda x: x.wear_count)
        
        # Recent additions
        recent_items = sorted(items, key=lambda x: x.date_added, reverse=True)[:5]
        
        return {
            'total_items': len(items),
            'categories': categories,
            'total_value': total_value,
            'most_worn': most_worn,
            'least_worn': least_worn,
            'recent_additions': recent_items,
            'total_outfits': len(self.outfit_history),
            'favorite_outfits': len(self.get_favorite_outfits())
        }
