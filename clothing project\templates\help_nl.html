{% extends "base.html" %}

{% block title %}Hulp & Uitleg - <PERSON><PERSON>lijke Kledingkast{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-question-circle me-3"></i>Hulp & Uitleg</h1>
    <p>Alles wat je moet weten over je persoonlijke kledingkast</p>
</div>

<div class="row">
    <div class="col-lg-3">
        <!-- Navigation Menu -->
        <div class="card sticky-top" style="top: 100px;">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Onderwerpen
                </h6>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <a href="#overzicht" class="list-group-item list-group-item-action">
                        <i class="fas fa-home me-2"></i>Overzicht
                    </a>
                    <a href="#kledingkast" class="list-group-item list-group-item-action">
                        <i class="fas fa-tshirt me-2"></i>Mijn Kledingkast
                    </a>
                    <a href="#outfit-generator" class="list-group-item list-group-item-action">
                        <i class="fas fa-magic me-2"></i>Outfit Generator
                    </a>
                    <a href="#custom-builder" class="list-group-item list-group-item-action">
                        <i class="fas fa-tools me-2"></i>Custom Builder
                    </a>
                    <a href="#geschiedenis" class="list-group-item list-group-item-action">
                        <i class="fas fa-history me-2"></i>Geschiedenis
                    </a>
                    <a href="#favorieten" class="list-group-item list-group-item-action">
                        <i class="fas fa-heart me-2"></i>Favorieten
                    </a>
                    <a href="#tips" class="list-group-item list-group-item-action">
                        <i class="fas fa-lightbulb me-2"></i>Tips & Tricks
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-9">
        <!-- Overzicht -->
        <section id="overzicht" class="mb-5">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-home me-2"></i>Overzicht</h3>
                </div>
                <div class="card-body">
                    <p class="lead">Welkom bij je persoonlijke AI-kledingkast! Deze app helpt je om:</p>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Je kledingkast digitaal te beheren</li>
                                <li><i class="fas fa-check text-success me-2"></i>Perfecte outfits te genereren</li>
                                <li><i class="fas fa-check text-success me-2"></i>Kleding te organiseren per gelegenheid</li>
                                <li><i class="fas fa-check text-success me-2"></i>Je stijl te ontdekken en ontwikkelen</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Outfit geschiedenis bij te houden</li>
                                <li><i class="fas fa-check text-success me-2"></i>Favoriete combinaties op te slaan</li>
                                <li><i class="fas fa-check text-success me-2"></i>Kleding te zoeken en filteren</li>
                                <li><i class="fas fa-check text-success me-2"></i>Statistieken van je kledingkast te bekijken</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Mijn Kledingkast -->
        <section id="kledingkast" class="mb-5">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-tshirt me-2"></i>Mijn Kledingkast</h3>
                </div>
                <div class="card-body">
                    <h5>Kleding Toevoegen</h5>
                    <p>Voeg nieuwe kledingstukken toe aan je digitale kledingkast:</p>
                    <ol>
                        <li><strong>Foto uploaden:</strong> Maak een foto van je kledingstuk of upload een bestaande foto</li>
                        <li><strong>Basisgegevens:</strong> Vul naam, categorie, merk, maat en materiaal in</li>
                        <li><strong>Kleuren:</strong> Geef de hoofdkleuren van het kledingstuk op (gescheiden door komma's)</li>
                        <li><strong>Stijlen:</strong> Selecteer welke stijlen bij dit kledingstuk passen</li>
                        <li><strong>Gelegenheden:</strong> Kies voor welke gelegenheden dit geschikt is</li>
                        <li><strong>Weer:</strong> Selecteer bij welk weer je dit zou dragen</li>
                        <li><strong>Formaliteit & Veelzijdigheid:</strong> Stel de schalen in (1-10)</li>
                    </ol>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Tip:</strong> Hoe meer details je invult, hoe beter de AI je outfits kan samenstellen!
                    </div>

                    <h5 class="mt-4">Kleding Beheren</h5>
                    <ul>
                        <li><strong>Bekijken:</strong> Klik op een kledingstuk om alle details te zien</li>
                        <li><strong>Bewerken:</strong> Gebruik de "Bewerken" knop om gegevens aan te passen</li>
                        <li><strong>Verwijderen:</strong> Gebruik de "Verwijderen" knop (let op: dit kan niet ongedaan gemaakt worden!)</li>
                        <li><strong>Zoeken:</strong> Gebruik de zoekbalk om specifieke items te vinden</li>
                        <li><strong>Filteren:</strong> Filter op categorie, kleur, stijl of gelegenheid</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Outfit Generator -->
        <section id="outfit-generator" class="mb-5">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-magic me-2"></i>Outfit Generator</h3>
                </div>
                <div class="card-body">
                    <p>De AI-outfit generator helpt je perfecte outfits samen te stellen op basis van je persoonlijke kledingkast.</p>
                    
                    <h5>Automatische Generatie</h5>
                    <p>Laat de AI complete outfits voor je maken:</p>
                    <ol>
                        <li><strong>Gelegenheid:</strong> Kies waarvoor je je wilt kleden (casual, werk, formeel, etc.)</li>
                        <li><strong>Weer:</strong> Selecteer het huidige of verwachte weer</li>
                        <li><strong>Stijlvoorkeur:</strong> Optioneel - kies een specifieke stijl</li>
                        <li><strong>Kleurvoorkeur:</strong> Optioneel - geef een gewenste kleur op</li>
                        <li><strong>Genereren:</strong> Klik op "Outfits Genereren" voor 3 suggesties</li>
                    </ol>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6><i class="fas fa-bolt text-warning me-2"></i>Snelle Presets</h6>
                            <ul class="small">
                                <li><strong>Werkdag:</strong> Professionele outfits voor kantoor</li>
                                <li><strong>Casual Dag:</strong> Comfortabele dagelijkse kleding</li>
                                <li><strong>Date Night:</strong> Romantische en stijlvolle looks</li>
                                <li><strong>Formeel Evenement:</strong> Elegante gelegenheidskleding</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-star text-success me-2"></i>AI Scoring</h6>
                            <p class="small">Elk outfit krijgt een score op basis van:</p>
                            <ul class="small">
                                <li>Stijl harmonie</li>
                                <li>Kleur combinatie</li>
                                <li>Geschiktheid voor gelegenheid</li>
                                <li>Weer geschiktheid</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Custom Builder -->
        <section id="custom-builder" class="mb-5">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-tools me-2"></i>Custom Builder - NIEUW!</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <i class="fas fa-star me-2"></i>
                        <strong>Nieuw!</strong> Nu kun je precies kiezen welke kledingstukken je wilt combineren!
                    </div>
                    
                    <p>Met de Custom Builder bepaal je zelf welke soorten kledingstukken je wilt gebruiken in je outfit.</p>
                    
                    <h5>Hoe werkt het?</h5>
                    <ol>
                        <li>Selecteer "Custom Builder" in plaats van "Auto Generate"</li>
                        <li>Kies welke kledingtypen je wilt gebruiken:</li>
                    </ol>

                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-tshirt text-primary me-2"></i>Beschikbare Kledingtypen:</h6>
                            <ul>
                                <li><strong>Tops:</strong> Shirts, blouses, truien</li>
                                <li><strong>Bottoms:</strong> Broeken, rokken, shorts</li>
                                <li><strong>Dresses:</strong> Jurken en jumpsuits</li>
                                <li><strong>Shoes:</strong> Alle soorten schoenen</li>
                                <li><strong>Outerwear:</strong> Jassen, vesten, blazers</li>
                                <li><strong>Accessories:</strong> Sieraden, tassen, riemen</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-rocket text-success me-2"></i>Snelle Custom Presets:</h6>
                            <ul>
                                <li><strong>Top + Bottom:</strong> Perfecte casual combinaties</li>
                                <li><strong>Dress Only:</strong> Alleen jurk suggesties</li>
                                <li><strong>Top Only:</strong> Alleen shirt/blouse ideeën</li>
                                <li><strong>Shoes Only:</strong> Schoenen aanbevelingen</li>
                            </ul>
                        </div>
                    </div>

                    <h5 class="mt-4">Voorbeelden van Custom Outfits:</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-tshirt fa-2x text-primary mb-2"></i>
                                    <h6>"Alleen een shirt en short"</h6>
                                    <small>Selecteer: Top + Bottom</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-female fa-2x text-success mb-2"></i>
                                    <h6>"Jurk met accessoires"</h6>
                                    <small>Selecteer: Dress + Accessories</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-shoe-prints fa-2x text-warning mb-2"></i>
                                    <h6>"Alleen schoenen"</h6>
                                    <small>Selecteer: Shoes Only</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Geschiedenis -->
        <section id="geschiedenis" class="mb-5">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-history me-2"></i>Outfit Geschiedenis</h3>
                </div>
                <div class="card-body">
                    <p>Houd bij welke outfits je hebt gegenereerd en gedragen:</p>
                    
                    <h5>Outfits Opslaan</h5>
                    <ul>
                        <li>Klik op "Outfit Opslaan" bij een gegenereerd outfit</li>
                        <li>Het outfit wordt automatisch opgeslagen in je geschiedenis</li>
                        <li>Je kunt later terugkijken welke combinaties je hebt geprobeerd</li>
                    </ul>

                    <h5>Geschiedenis Bekijken</h5>
                    <ul>
                        <li>Ga naar "Outfit History" in het menu</li>
                        <li>Zie alle opgeslagen outfits chronologisch geordend</li>
                        <li>Bekijk details van elk outfit</li>
                        <li>Markeer outfits als favoriet</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Favorieten -->
        <section id="favorieten" class="mb-5">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-heart me-2"></i>Favorieten</h3>
                </div>
                <div class="card-body">
                    <p>Bewaar je beste outfits als favorieten voor snelle toegang:</p>
                    
                    <ul>
                        <li><strong>Favoriet maken:</strong> Klik op het hartje bij een outfit</li>
                        <li><strong>Favorieten bekijken:</strong> Ga naar de "Favorieten" pagina</li>
                        <li><strong>Snel hergebruiken:</strong> Gebruik favoriete outfits als inspiratie</li>
                        <li><strong>Organiseren:</strong> Groepeer je beste looks per gelegenheid</li>
                    </ul>

                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Tip:</strong> Maak foto's van jezelf in je favoriete outfits om te zien hoe ze in het echt staan!
                    </div>
                </div>
            </div>
        </section>

        <!-- Tips & Tricks -->
        <section id="tips" class="mb-5">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-lightbulb me-2"></i>Tips & Tricks</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-camera text-primary me-2"></i>Foto Tips</h5>
                            <ul class="small">
                                <li>Maak foto's bij goed licht</li>
                                <li>Leg kleding plat neer of hang het op</li>
                                <li>Zorg voor een neutrale achtergrond</li>
                                <li>Maak de foto van dichtbij voor details</li>
                            </ul>

                            <h5><i class="fas fa-tags text-success me-2"></i>Labeling Tips</h5>
                            <ul class="small">
                                <li>Wees specifiek met kleuren (bijv. "donkerblauw" i.p.v. "blauw")</li>
                                <li>Voeg alle relevante stijlen toe</li>
                                <li>Denk goed na over gelegenheden</li>
                                <li>Wees eerlijk over formaliteit niveau</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-magic text-warning me-2"></i>Generator Tips</h5>
                            <ul class="small">
                                <li>Probeer verschillende stijlvoorkeuren</li>
                                <li>Experimenteer met kleurcombinaties</li>
                                <li>Gebruik Custom Builder voor specifieke wensen</li>
                                <li>Sla goede combinaties op als favoriet</li>
                            </ul>

                            <h5><i class="fas fa-chart-line text-info me-2"></i>Optimalisatie Tips</h5>
                            <ul class="small">
                                <li>Voeg regelmatig nieuwe kleding toe</li>
                                <li>Update seizoensgebonden items</li>
                                <li>Verwijder kleding die je niet meer draagt</li>
                                <li>Bekijk je statistieken voor inzichten</li>
                            </ul>
                        </div>
                    </div>

                    <div class="alert alert-success mt-4">
                        <h6><i class="fas fa-star me-2"></i>Pro Tip: Maximale AI Prestaties</h6>
                        <p class="mb-0">Hoe meer gedetailleerde informatie je invoert over je kleding (kleuren, stijlen, gelegenheden), hoe beter de AI je persoonlijke smaak leert kennen en hoe accurater de outfit suggesties worden!</p>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
    border-radius: 15px;
    text-align: center;
}

.list-group-item-action:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

.card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
}

section {
    scroll-margin-top: 100px;
}

.alert {
    border: none;
    border-radius: 10px;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.small {
    font-size: 0.9rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Highlight active section in navigation
window.addEventListener('scroll', function() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.list-group-item-action');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop - 120;
        if (window.pageYOffset >= sectionTop) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === '#' + current) {
            link.classList.add('active');
        }
    });
});
</script>
{% endblock %}
